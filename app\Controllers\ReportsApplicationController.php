<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\ExerciseModel;
use App\Models\PositionsModel;
use App\Models\AppxApplicationDetailsModel;

/**
 * ReportsApplicationController
 *
 * Controller for application-related reports including application register and pre-screening reports
 */
class ReportsApplicationController extends Controller
{
    protected $exerciseModel;
    protected $positionsModel;
    protected $applicationModel;

    public function __construct()
    {
        helper(['url', 'form']);
        $this->exerciseModel = new ExerciseModel();
        $this->positionsModel = new PositionsModel();
        $this->applicationModel = new AppxApplicationDetailsModel();
    }

    /**
     * [GET] Application Register Report
     * URI: /reports/application-register/{exerciseId}
     */
    public function applicationRegister($exerciseId)
    {
        try {
            // Get exercise data
            $exercise = $this->exerciseModel->find($exerciseId);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get all applications for this exercise
            $applications = $this->applicationModel->where('exercise_id', $exerciseId)->findAll();

            // Get all positions for this exercise
            $positions = $this->positionsModel->where('exercise_id', $exerciseId)->where('deleted_at IS NULL')->findAll();

            // Calculate statistics
            $totalApplications = count($applications);
            $totalApplicants = count(array_unique(array_column($applications, 'applicant_id')));
            $positionsWithApplications = 0;
            $positionsWithoutApplications = 0;

            foreach ($positions as $position) {
                $hasApplications = false;
                foreach ($applications as $application) {
                    if ($application['position_id'] == $position['id']) {
                        $hasApplications = true;
                        break;
                    }
                }
                if ($hasApplications) {
                    $positionsWithApplications++;
                } else {
                    $positionsWithoutApplications++;
                }
            }

            $statistics = [
                'total_applications' => $totalApplications,
                'total_applicants' => $totalApplicants,
                'total_positions_with_applications' => $positionsWithApplications,
                'total_positions_without_applications' => $positionsWithoutApplications
            ];

            $data = [
                'title' => 'Application Register Report - ' . $exercise['exercise_name'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'applications' => $applications,
                'statistics' => $statistics
            ];

            return view('application_reports/appx_reports_application_register', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching application register data: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading application register');
        }
    }

    /**
     * [GET] Pre-Screening Report
     * URI: /reports/pre-screening/{positionId}
     */
    public function preScreening($positionId)
    {
        // Mock position data
        $position = $this->getMockPosition($positionId);
        if (!$position) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Position not found');
        }

        // Mock pre-screening data with pass/fail status and remarks
        $preScreeningResults = [
            [
                'id' => 1,
                'application_number' => 'APP-2024-001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'gender' => 'Male',
                'contact_details' => '{"phone": "71234567", "email": "<EMAIL>"}',
                'pre_screening_status' => 'passed',
                'pre_screening_score' => 85,
                'pre_screening_remarks' => 'Meets all minimum qualifications. Strong technical background.',
                'screened_by' => 'HR Officer 1',
                'screened_at' => '2024-02-05 10:30:00',
                'qualifications_met' => 'Yes',
                'experience_met' => 'Yes',
                'documents_complete' => 'Yes'
            ],
            [
                'id' => 2,
                'application_number' => 'APP-2024-002',
                'first_name' => 'Mary',
                'last_name' => 'Smith',
                'gender' => 'Female',
                'contact_details' => '{"phone": "72345678", "email": "<EMAIL>"}',
                'pre_screening_status' => 'passed',
                'pre_screening_score' => 92,
                'pre_screening_remarks' => 'Excellent qualifications and extensive experience. Highly recommended.',
                'screened_by' => 'HR Officer 2',
                'screened_at' => '2024-02-05 14:15:00',
                'qualifications_met' => 'Yes',
                'experience_met' => 'Yes',
                'documents_complete' => 'Yes'
            ],
            [
                'id' => 3,
                'application_number' => 'APP-2024-003',
                'first_name' => 'Peter',
                'last_name' => 'Johnson',
                'gender' => 'Male',
                'contact_details' => '{"phone": "73456789", "email": "<EMAIL>"}',
                'pre_screening_status' => 'failed',
                'pre_screening_score' => 45,
                'pre_screening_remarks' => 'Does not meet minimum experience requirements. Missing key certifications.',
                'screened_by' => 'HR Officer 1',
                'screened_at' => '2024-02-06 09:20:00',
                'qualifications_met' => 'Partial',
                'experience_met' => 'No',
                'documents_complete' => 'Yes'
            ],
            [
                'id' => 4,
                'application_number' => 'APP-2024-004',
                'first_name' => 'Sarah',
                'last_name' => 'Wilson',
                'gender' => 'Female',
                'contact_details' => '{"phone": "74567890", "email": "<EMAIL>"}',
                'pre_screening_status' => 'passed',
                'pre_screening_score' => 78,
                'pre_screening_remarks' => 'Meets minimum requirements. Good potential candidate.',
                'screened_by' => 'HR Officer 2',
                'screened_at' => '2024-02-06 11:45:00',
                'qualifications_met' => 'Yes',
                'experience_met' => 'Yes',
                'documents_complete' => 'Yes'
            ]
        ];

        $data = [
            'title' => 'Pre-Screening Report - ' . $position['designation'],
            'menu' => 'reports',
            'position' => $position,
            'pre_screening_results' => $preScreeningResults
        ];

        return view('application_reports/appx_reports_pre_screening', $data);
    }

    /**
     * [POST] Export Application Register Report
     * URI: /reports/application-register/export
     */
    public function exportApplicationRegister()
    {
        // Mock export response for UI development
        $response = [
            'success' => true,
            'message' => 'Application register report exported successfully',
            'file_url' => base_url('exports/application_register_' . date('Y-m-d_H-i-s') . '.xlsx')
        ];

        return $this->response->setJSON($response);
    }

    /**
     * [POST] Export Pre-Screening Report
     * URI: /reports/pre-screening/export
     */
    public function exportPreScreening()
    {
        // Mock export response for UI development
        $response = [
            'success' => true,
            'message' => 'Pre-screening report exported successfully',
            'file_url' => base_url('exports/pre_screening_report_' . date('Y-m-d_H-i-s') . '.xlsx')
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Get mock position data
     */
    private function getMockPosition($positionId)
    {
        $positions = [
            1 => [
                'id' => 1,
                'exercise_id' => 1,
                'position_group_id' => 1,
                'position_reference' => 'IT-001',
                'designation' => 'Senior Software Engineer',
                'classification' => 'Grade 12',
                'award' => 'K85,000 - K95,000',
                'location' => 'Port Moresby',
                'group_name' => 'Information Technology',
                'exercise_name' => 'IT Department Recruitment Exercise 2024'
            ],
            2 => [
                'id' => 2,
                'exercise_id' => 1,
                'position_group_id' => 1,
                'position_reference' => 'IT-002',
                'designation' => 'Database Administrator',
                'classification' => 'Grade 11',
                'award' => 'K75,000 - K85,000',
                'location' => 'Port Moresby',
                'group_name' => 'Information Technology',
                'exercise_name' => 'IT Department Recruitment Exercise 2024'
            ],
            3 => [
                'id' => 3,
                'exercise_id' => 1,
                'position_group_id' => 2,
                'position_reference' => 'SYS-001',
                'designation' => 'System Administrator',
                'classification' => 'Grade 10',
                'award' => 'K65,000 - K75,000',
                'location' => 'Port Moresby',
                'group_name' => 'System Administration',
                'exercise_name' => 'IT Department Recruitment Exercise 2024'
            ]
        ];

        return $positions[$positionId] ?? null;
    }
}
